DB_URL="mongodb://AdminCherry:HOM@21939330@localhost:27017/orbit"
//For docker
//DB_URL=*************************************************************************
JWT_SECRET="HOM@21939330"

issuer="<EMAIL>"
audience="<EMAIL>"

NODE_ENV="production"
EDIT_MODE ="false"
ignoreEnvFile="false"
PORT=3000

ControlPanelAdminPassword= "HOM@21939330"
ControlPanelAdminPasswordViewer= "HOM@21939330"

isOneSignalEnabled ="false"
isFirebaseFcmEnabled ="true"

oneSignalAppId="************************************"
oneSignalApiKey="os_v2_app_hidtuivxmzhr3axkgux4tsfwn6ltqhi5umyeqc44flara6kba3pfxjlcr255auhtclw3363ub6gbvmiyif76qqwyluwdosxm7bgtdey"

EMAIL_HOST="smtp.gmail.com"
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="xskjtjkjgdxqwkmf"
AGORA_APP_ID="********************************"
AGORA_APP_CERTIFICATE="********************************"

INLINE_RUNTIME_CHUNK=false

# apnKeyId=YOUR KEY ID
# apnAppBundle=YOUR APPLE BUNDLE
# appleAccountTeamId=YOUR APPLE TEAM ID