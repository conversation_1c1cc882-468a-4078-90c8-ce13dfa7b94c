/**
 * Copyright 2023, the hate<PERSON><PERSON>b project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import {Injectable, NotFoundException, ForbiddenException} from "@nestjs/common";
import {InjectModel} from "@nestjs/mongoose";
import {PaginateModel} from "mongoose";
import {IMessage} from "../../../chat/message/entities/message.entity";
import {MessageService} from "../../../chat/message/message.service";
import {RoomMiddlewareService} from "../../../chat/room_middleware/room_middleware.service";
import {IRoomMember} from "../../../chat/room_member/entities/room_member.entity";
import {SocketIoService} from "../../../chat/socket_io/socket_io.service";
import {SocketEventsType, MessageType} from "../../../core/utils/enums";
import fs from "fs";
import path from "path";
import root from "app-root-path";

@Injectable()
export class UserFilesService {
    constructor(
        @InjectModel("message") private readonly messageModel: PaginateModel<IMessage>,
        @InjectModel("room_member") private readonly roomMemberModel: PaginateModel<IRoomMember>,
        private readonly messageService: MessageService,
        private readonly roomMiddlewareService: RoomMiddlewareService,
        private readonly socketIoService: SocketIoService
    ) {
    }

    async getUserFiles(userId: string, params: any) {
        const page = parseInt(params.page) || 1;
        const limit = parseInt(params.limit) || 20;
        const fileType = params.fileType; // 'image', 'video', 'file', 'voice'

        // Get user's room IDs
        const userRoomIds = await this.getUserRoomIds(userId);

        // Build aggregation pipeline to get files from messages
        const matchStage: any = {
            $or: [
                { sId: userId }, // Files sent by user
                {
                    rId: { $in: userRoomIds } // Files in user's rooms
                }
            ],
            msgAtt: { $ne: null },
            dltAt: null, // Not deleted
            $and: [
                { dF: { $ne: userId } } // Not deleted by this user
            ]
        };

        // Filter by file type if specified
        if (fileType) {
            matchStage.mT = this.getMessageTypeFromFileType(fileType);
        }

        // Use simple query approach since aggregation is complex
        let query: any = {
            $or: [
                { sId: userId },
                { rId: { $in: userRoomIds } }
            ],
            msgAtt: { $ne: null },
            dltAt: null
        };

        // Add file type filter if specified
        if (fileType && fileType !== 'all') {
            const messageTypes = this.getMessageTypesForFileType(fileType);
            query.mT = { $in: messageTypes };
        }

        const allFiles = await this.messageModel.find(query)
            .sort({ createdAt: -1 })
            .exec();

        console.log(`Simple query found ${allFiles.length} messages with attachments`);

        // Convert to the expected format and filter files that exist on filesystem
        const existingFiles = [];
        for (const message of allFiles) {
            const msgAtt = message.msgAtt as any;
            if (msgAtt && msgAtt.url) {
                // The url contains the full path: "userId/filename"
                const filePath = path.join(root.path, "public", "media", msgAtt.url);
                console.log(`Checking file existence: ${msgAtt.url} -> ${filePath}`);
                if (fs.existsSync(filePath)) {
                    existingFiles.push({
                        id: message._id,
                        messageId: message._id,
                        senderId: message.sId,
                        senderName: message.sName,
                        roomId: message.rId,
                        messageType: message.mT,
                        fileName: msgAtt.name,
                        fileSize: msgAtt.fileSize || 0,
                        fileHash: msgAtt.fileHash,
                        extension: this.getExtensionFromUrl(msgAtt.url),
                        mimeType: msgAtt.mimeType,
                        networkUrl: msgAtt.url,
                        createdAt: message.createdAt,
                        fileType: this.getFileTypeFromMessageType(message.mT)
                    });
                } else {
                    // File doesn't exist, mark message as having missing attachment
                    console.log(`Missing file detected: ${msgAtt.url} for message ${message._id}`);
                }
            }
        }

        // Apply pagination to existing files
        const startIndex = (page - 1) * limit;
        const paginatedFiles = existingFiles.slice(startIndex, startIndex + limit);

        return {
            files: paginatedFiles,
            pagination: {
                page,
                limit,
                total: existingFiles.length,
                pages: Math.ceil(existingFiles.length / limit)
            }
        };
    }

    async deleteFile(messageId: string, userId: string) {
        // Get the message to verify ownership and get file info
        const message = await this.messageService.getByIdOrFail(messageId);
        
        // Check if user has permission to delete (sender or in the room)
        const hasPermission = message.sId.toString() === userId ||
                            await this.isUserInRoom(userId, message.rId.toString());

        if (!hasPermission) {
            throw new ForbiddenException("You don't have permission to delete this file");
        }

        // Delete physical file from storage
        if (message.msgAtt && (message.msgAtt as any).url) {
            await this.deletePhysicalFile((message.msgAtt as any).url);
        }

        // Remove attachment from message and convert to text message
        await this.messageModel.findByIdAndUpdate(messageId, {
            msgAtt: null, // Clear attachment data
            c: "📎 File deleted", // Update content to show file was deleted
            mT: MessageType.Text, // Change message type to text since attachment is removed
        });

        // Get the updated message and emit socket event to notify all clients
        const updatedMessage = await this.messageModel.findById(messageId);
        if (updatedMessage) {
            // Emit the updated message to all room members
            this.socketIoService.io
                .to(updatedMessage.rId.toString())
                .emit(SocketEventsType.v1OnUpdateMessage, JSON.stringify(updatedMessage));
        }
    }

    async deleteMultipleFiles(messageIds: string[], userId: string) {
        for (const messageId of messageIds) {
            await this.deleteFile(messageId, userId);
        }
    }

    async cleanupOrphanedFiles(userId: string) {
        // Find all messages with attachments for this user
        const userRoomIds = await this.getUserRoomIds(userId);

        const messages = await this.messageModel.find({
            $or: [
                { sId: userId },
                { rId: { $in: userRoomIds } }
            ],
            msgAtt: { $ne: null },
            dltAt: null
        });

        let cleanedCount = 0;
        for (const message of messages) {
            const msgAtt = message.msgAtt as any;
            if (msgAtt && msgAtt.url) {
                const filePath = path.join(root.path, "public", "media", msgAtt.url);
                if (!fs.existsSync(filePath)) {
                    // File doesn't exist, remove attachment but keep message
                    await this.messageModel.findByIdAndUpdate(message._id, {
                        msgAtt: null,
                        c: "📎 File no longer available",
                        isEdited: true
                    });
                    cleanedCount++;
                    console.log(`Cleaned orphaned message: ${message._id} with missing file: ${msgAtt.url}`);
                }
            }
        }

        return { cleanedCount };
    }

    private getMessageTypesForFileType(fileType: string): string[] {
        switch (fileType) {
            case 'image':
                return ['image']; // Image message type
            case 'video':
                return ['video']; // Video message type
            case 'file':
                return ['file']; // File message type
            case 'voice':
                return ['voice']; // Voice message type
            default:
                return ['image', 'video', 'file', 'voice']; // All file types
        }
    }

    private getExtensionFromUrl(url: string): string {
        const parts = url.split('.');
        return parts.length > 1 ? `.${parts[parts.length - 1]}` : '';
    }

    private async deletePhysicalFile(fileUrl: string) {
        try {
            // fileUrl is like "6869c00fb29d342c627d6f94/media600-1fb422bc-756d-42de-9d6f-d15c0a7c49e4.jpg"
            const filePath = path.join(root.path, "public", "media", fileUrl);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                console.log(`Successfully deleted file: ${fileUrl}`);
            } else {
                console.log(`File not found for deletion: ${fileUrl}`);
            }
        } catch (error) {
            console.error(`Error deleting physical file ${fileUrl}: ${error.message}`);
            // Don't throw error as database cleanup is more important
        }
    }

    private async getUserRoomIds(userId: string): Promise<string[]> {
        const roomMembers = await this.roomMemberModel.find(
            { uId: userId, isD: false },
            'rId'
        ).lean();
        return roomMembers.map(rm => rm.rId.toString());
    }

    private async isUserInRoom(userId: string, roomId: string): Promise<boolean> {
        try {
            const roomMember = await this.roomMiddlewareService.isThereRoomMember(roomId, userId);
            return roomMember !== null;
        } catch (error) {
            return false;
        }
    }

    private getMessageTypeFromFileType(fileType: string): string {
        switch (fileType.toLowerCase()) {
            case 'image': return 'Image';
            case 'video': return 'Video';
            case 'file': return 'File';
            case 'voice': return 'Voice';
            default: return 'File';
        }
    }

    private getFileTypeFromMessageType(messageType: string): string {
        switch (messageType.toLowerCase()) {
            case 'image': return 'image';
            case 'video': return 'video';
            case 'voice': return 'voice';
            case 'file': return 'file';
            default: return 'file';
        }
    }
}
