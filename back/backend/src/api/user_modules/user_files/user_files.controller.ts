/**
 * Copyright 2023, the hatemragab project author.
 * All rights reserved. Use of this source code is governed by a
 * MIT license that can be found in the LICENSE file.
 */

import {
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Query,
    Req,
    UseGuards,
    Body
} from "@nestjs/common";
import {UserFilesService} from "./user_files.service";
import {VerifiedAuthGuard} from "../../../core/guards/verified.auth.guard";
import {V1Controller} from "../../../core/common/v1-controller.decorator";
import {resOK} from "../../../core/utils/res.helpers";
import {MongoIdDto} from "../../../core/common/dto/mongo.id.dto";
import {DeleteFilesDto} from "./dto/delete-files.dto";

@UseGuards(VerifiedAuthGuard)
@V1Controller('user/files')
export class UserFilesController {
    constructor(
        private readonly userFilesService: UserFilesService
    ) {
    }

    @Get()
    async getUserFiles(@Req() req: any, @Query() query: any) {
        const files = await this.userFilesService.getUserFiles(req.user._id, query);
        return resOK(files);
    }

    @Delete(':fileId')
    async deleteFile(@Param() params: MongoIdDto, @Req() req: any) {
        await this.userFilesService.deleteFile(params.id, req.user._id);
        return resOK({ message: 'File deleted successfully' });
    }

    @Delete()
    async deleteMultipleFiles(@Body() dto: DeleteFilesDto, @Req() req: any) {
        await this.userFilesService.deleteMultipleFiles(dto.fileIds, req.user._id);
        return resOK({ message: 'Files deleted successfully' });
    }

    @Post('cleanup')
    async cleanupOrphanedFiles(@Req() req: any) {
        const result = await this.userFilesService.cleanupOrphanedFiles(req.user._id);
        return resOK({
            message: `Cleaned up ${result.cleanedCount} orphaned file references`,
            cleanedCount: result.cleanedCount
        });
    }
}
