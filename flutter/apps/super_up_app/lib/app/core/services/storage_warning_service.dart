import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:super_up_core/super_up_core.dart';

class StorageWarningService extends ChangeNotifier {
  static final StorageWarningService _instance =
      StorageWarningService._internal();
  factory StorageWarningService() => _instance;
  StorageWarningService._internal();

  bool _showWarning = false;
  double _storagePercentage = 0.0;
  int _currentStorageBytes = 0;
  static const double _maxStorageBytes = 1024 * 1024 * 1024; // 1GB in bytes
  static const double _warningThreshold = 0.7; // 70%

  bool get showWarning => _showWarning;
  double get storagePercentage => _storagePercentage;
  int get currentStorageBytes => _currentStorageBytes;
  double get maxStorageBytes => _maxStorageBytes;

  Future<void> checkStorageUsage() async {
    try {
      final dir = Directory(VFileUtils.downloadPath());
      if (!await dir.exists()) {
        _updateStorageInfo(0);
        return;
      }

      final files = await dir.list(recursive: true).toList();
      int totalSize = 0;

      for (var file in files) {
        try {
          if (file is File) {
            final stat = await file.stat();
            totalSize += stat.size;
          }
        } catch (e) {
          // Skip files that can't be accessed
        }
      }

      _updateStorageInfo(totalSize);
    } catch (e) {
      _updateStorageInfo(0);
    }
  }

  void _updateStorageInfo(int sizeInBytes) {
    _currentStorageBytes = sizeInBytes;
    _storagePercentage = (sizeInBytes / _maxStorageBytes).clamp(0.0, 1.0);
    _showWarning =
        _storagePercentage >= _warningThreshold; // Show warning at 70%
    notifyListeners();
  }

  void dismissWarning() {
    _showWarning = false;
    notifyListeners();
  }

  String formatBytes(double bytes) {
    if (bytes < 1024) return '${bytes.toStringAsFixed(1)} B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  String get storageUsageText {
    return '${formatBytes(_currentStorageBytes.toDouble())} / ${formatBytes(_maxStorageBytes)}';
  }

  String get warningMessage {
    final percentage = (_storagePercentage * 100).toStringAsFixed(0);
    return 'Storage is $percentage% full. Upgrade plan or clear storage to free up space.';
  }
}
