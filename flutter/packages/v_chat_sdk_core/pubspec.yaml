name: v_chat_sdk_core
description: core package for v chat sdk witch use socket.io with node js and mongodb db
version: 1.1.0
homepage: https://v-chat-sdk.github.io/vchat-v2-docs/docs/intro
issue_tracker: https://github.com/hatemragab/v_chat_sdk/issues
repository: https://github.com/hatemragab/v_chat_sdk/tree/master/core_packages/v_chat_sdk_core
publish_to: none

environment:
  sdk: '>=3.0.2 <4.0.0'
  flutter: ">=1.17.0"
dependencies:
  flutter:
    sdk: flutter
  intl: ^0.20.2
  http: ^1.2.2
  logging: ^1.3.0
  chopper: ^8.0.3
  http_parser: ^4.0.2
  encrypt: 5.0.1
  crypto: ^3.0.6
  pointycastle: ^3.9.1
  socket_io_client: ^3.0.0
  latlong2: ^0.9.1
  path: ^1.9.0

  sqflite: ^2.4.0
  sqflite_common_ffi: ^2.3.4
  objectid: ^3.1.0
  sqlite3_flutter_libs: ^0.5.26
  diacritic: ^0.1.6
  flutter_fgbg: ^0.6.0
  flutter_cache_manager: ^3.4.1
  path_provider: ^2.1.5
  google_api_availability: ^5.0.1
  shared_preferences: ^2.3.3
  device_info_plus: ^11.1.0
  event_bus_plus: ^0.6.2
  uuid: ^4.5.1
  collection: ^1.18.0
  v_platform: ^2.1.4
  timeago: ^3.7.0
  flutter_parsed_text: ^2.2.1
  background_downloader:  ^8.8.0
  super_up_core:
    path: ../super_up_core
  platform_local_notifications:
    path: ../platform_local_notifications
  enum_to_string: ^2.0.1
  connectivity_plus: ^6.1.0
  permission_handler: ^11.3.1
  flutter_callkit_incoming:
    git:
      url: https://github.com/hatemragab/flutter_callkit_incoming.git
      ref: dev
dev_dependencies:
  lint: ^2.3.0
  build_runner: ^2.4.13
  chopper_generator: ^8.0.3
  lints: ^5.0.0
  flutter_lints: ^5.0.0

flutter:

platforms:
  android:
  ios:
  web:
  windows:
  macos:
